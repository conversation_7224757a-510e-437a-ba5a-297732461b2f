server:
  port: 8082

spring:
  application:
    name: kafka-performance-test
  
  # Use H2 in-memory database for standalone mode
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    database-platform: org.hibernate.dialect.H2Dialect
  
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Disable Kafka for standalone mode
  kafka:
    bootstrap-servers: ${KAFKA_BROKERS:localhost:29092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: 1
      retries: 0
      batch-size: 1000
    consumer:
      group-id: standalone-test-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: true
      properties:
        spring.json.trusted.packages: "com.spark.test.model"

# Custom Kafka Configuration for standalone
kafka:
  topics:
    order-events:
      name: order-events
      partitions: 2
      replication-factor: 1
    user-events:
      name: user-events
      partitions: 2
      replication-factor: 1
    payment-events:
      name: payment-events
      partitions: 2
      replication-factor: 1

# Performance Testing Configuration
performance:
  batch-size: 100
  thread-pool-size: 2
  max-messages-per-test: 1000

# Actuator for monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.spark.test: INFO
    org.springframework.kafka: WARN
    org.apache.kafka: WARN
    org.springframework.web: INFO
