# Simple test for standalone application
# Tests basic functionality without Kafka

Write-Host "=== Simple Application Test ===" -ForegroundColor Green

$baseUrl = "http://localhost:8082"

# Function to test endpoint
function Test-API {
    param($endpoint, $method = "GET", $description)
    
    Write-Host "`nTesting: $description" -ForegroundColor Yellow
    $url = "$baseUrl$endpoint"
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method $method -TimeoutSec 10
        Write-Host "✓ SUCCESS" -ForegroundColor Green
        
        # Pretty print JSON response
        if ($response) {
            $json = $response | ConvertTo-Json -Depth 3
            Write-Host $json -ForegroundColor Cyan
        }
        
        return $response
    }
    catch {
        Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Test basic endpoints
Write-Host "Testing basic application endpoints..." -ForegroundColor Yellow

# Health check
$health = Test-API "/api/performance/health" "GET" "Health Check"

if (-not $health) {
    Write-Host "`nApplication is not running on port 8082!" -ForegroundColor Red
    Write-Host "Please start the application first:" -ForegroundColor Yellow
    Write-Host "  .\run-standalone.ps1" -ForegroundColor White
    exit 1
}

# Consumer stats
Test-API "/api/performance/stats/consumer" "GET" "Consumer Statistics"

# Metrics summary  
Test-API "/api/performance/metrics/summary" "GET" "Metrics Summary"

# Metrics history
Test-API "/api/performance/metrics/history" "GET" "Metrics History"

# Reset counters
Test-API "/api/performance/reset" "POST" "Reset Counters"

Write-Host "`n=== Basic Tests Completed ===" -ForegroundColor Green
Write-Host "All basic endpoints are working!" -ForegroundColor Green

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "  • To test with Kafka: Start Docker and run full tests" -ForegroundColor White
Write-Host "  • H2 Console: http://localhost:8082/h2-console" -ForegroundColor Cyan
Write-Host "  • Application: http://localhost:8082/api/performance/health" -ForegroundColor Cyan
