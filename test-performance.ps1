# Kafka Performance Test Script
# PowerShell script để test hiệu năng Kafka

Write-Host "=== Kafka Performance Testing Script ===" -ForegroundColor Green

# Function to check if service is running
function Test-ServiceHealth {
    param($url, $serviceName)
    try {
        $response = Invoke-RestMethod -Uri $url -Method Get -TimeoutSec 10
        Write-Host "✓ $serviceName is running" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "✗ $serviceName is not responding" -ForegroundColor Red
        return $false
    }
}

# Function to run performance test
function Invoke-PerformanceTest {
    param($testType, $messages, $threads)
    
    Write-Host "`nRunning $testType test with $messages messages and $threads threads..." -ForegroundColor Yellow
    
    $url = "http://localhost:8081/api/performance/test/$testType"
    $params = "?totalMessages=$messages&producerThreads=$threads"
    
    try {
        $startTime = Get-Date
        $response = Invoke-RestMethod -Uri ($url + $params) -Method Post -TimeoutSec 300
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        
        Write-Host "✓ Test completed in $([math]::Round($duration, 2)) seconds" -ForegroundColor Green
        Write-Host "  Messages/sec: $([math]::Round($response.messagesPerSecond, 2))" -ForegroundColor Cyan
        Write-Host "  Processed: $($response.processedMessages)/$($response.expectedMessages)" -ForegroundColor Cyan
        
        return $response
    }
    catch {
        Write-Host "✗ Test failed: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Function to get consumer stats
function Get-ConsumerStats {
    try {
        $stats = Invoke-RestMethod -Uri "http://localhost:8081/api/performance/stats/consumer" -Method Get
        Write-Host "`nConsumer Statistics:" -ForegroundColor Yellow
        Write-Host "  Order Events: $($stats.processedOrderCount)" -ForegroundColor Cyan
        Write-Host "  User Events: $($stats.processedUserCount)" -ForegroundColor Cyan
        Write-Host "  Payment Events: $($stats.processedPaymentCount)" -ForegroundColor Cyan
        Write-Host "  Total: $($stats.totalProcessed)" -ForegroundColor Cyan
        return $stats
    }
    catch {
        Write-Host "Failed to get consumer stats: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Function to reset counters
function Reset-Counters {
    try {
        Invoke-RestMethod -Uri "http://localhost:8081/api/performance/reset" -Method Post | Out-Null
        Write-Host "✓ Counters reset successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to reset counters: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main execution
Write-Host "`n1. Checking service health..." -ForegroundColor Yellow

# Check if Spring Boot app is running
if (-not (Test-ServiceHealth "http://localhost:8081/api/performance/health" "Spring Boot Application")) {
    Write-Host "Please start the Spring Boot application first!" -ForegroundColor Red
    Write-Host "Run: ./gradlew bootRun" -ForegroundColor Yellow
    exit 1
}

# Check Docker services
Write-Host "`n2. Checking Docker services..." -ForegroundColor Yellow
$dockerServices = @(
    @{name="Kafka"; port=29092},
    @{name="PostgreSQL"; port=15432},
    @{name="Zookeeper"; port=2181}
)

foreach ($service in $dockerServices) {
    $connection = Test-NetConnection -ComputerName localhost -Port $service.port -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "✓ $($service.name) is running on port $($service.port)" -ForegroundColor Green
    } else {
        Write-Host "✗ $($service.name) is not running on port $($service.port)" -ForegroundColor Red
    }
}

# Reset counters before starting tests
Write-Host "`n3. Resetting counters..." -ForegroundColor Yellow
Reset-Counters

# Run performance tests
Write-Host "`n4. Running Performance Tests..." -ForegroundColor Yellow

# Small scale tests
Write-Host "`n--- Small Scale Tests ---" -ForegroundColor Magenta
$orderResult1 = Invoke-PerformanceTest "order-events" 1000 2
$userResult1 = Invoke-PerformanceTest "user-events" 500 1
$paymentResult1 = Invoke-PerformanceTest "payment-events" 800 2

Start-Sleep -Seconds 5
Get-ConsumerStats

# Medium scale tests
Write-Host "`n--- Medium Scale Tests ---" -ForegroundColor Magenta
Reset-Counters
$orderResult2 = Invoke-PerformanceTest "order-events" 10000 4
$userResult2 = Invoke-PerformanceTest "user-events" 5000 2
$paymentResult2 = Invoke-PerformanceTest "payment-events" 8000 4

Start-Sleep -Seconds 10
Get-ConsumerStats

# Large scale test
Write-Host "`n--- Large Scale Test ---" -ForegroundColor Magenta
Reset-Counters
$mixedResult = Invoke-PerformanceTest "mixed-events" 30000 6

Start-Sleep -Seconds 15
Get-ConsumerStats

# Get final metrics summary
Write-Host "`n5. Getting Metrics Summary..." -ForegroundColor Yellow
try {
    $summary = Invoke-RestMethod -Uri "http://localhost:8081/api/performance/metrics/summary" -Method Get
    Write-Host "`nFinal Metrics Summary:" -ForegroundColor Yellow
    Write-Host "  Total Processed: $($summary.totalProcessedMessages)" -ForegroundColor Cyan
    Write-Host "  Success Count: $($summary.successCount)" -ForegroundColor Cyan
    Write-Host "  Error Count: $($summary.errorCount)" -ForegroundColor Cyan
    Write-Host "  Success Rate: $([math]::Round($summary.successRate, 2))%" -ForegroundColor Cyan
    
    Write-Host "`nAverage Processing Times:" -ForegroundColor Yellow
    foreach ($topic in $summary.avgProcessingTimesByTopic.PSObject.Properties) {
        if ($topic.Value) {
            Write-Host "  $($topic.Name): $([math]::Round($topic.Value, 2)) ms" -ForegroundColor Cyan
        }
    }
}
catch {
    Write-Host "Failed to get metrics summary: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Performance Testing Completed ===" -ForegroundColor Green
Write-Host "Check Grafana dashboard at: http://localhost:3000" -ForegroundColor Yellow
Write-Host "Check Prometheus metrics at: http://localhost:8081/actuator/prometheus" -ForegroundColor Yellow
