package com.spark.test.repository;

import com.spark.test.entity.ProcessedMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ProcessedMessageRepository extends JpaRepository<ProcessedMessage, Long> {
    
    List<ProcessedMessage> findByTopicAndProcessedAtBetween(
            String topic, 
            LocalDateTime startTime, 
            LocalDateTime endTime
    );
    
    @Query("SELECT COUNT(p) FROM ProcessedMessage p WHERE p.topic = :topic AND p.status = :status")
    Long countByTopicAndStatus(@Param("topic") String topic, @Param("status") String status);
    
    @Query("SELECT AVG(p.processingTimeMs) FROM ProcessedMessage p WHERE p.topic = :topic")
    Double getAverageProcessingTimeByTopic(@Param("topic") String topic);
    
    @Query("SELECT p.threadName, COUNT(p) FROM ProcessedMessage p WHERE p.topic = :topic GROUP BY p.threadName")
    List<Object[]> getMessageCountByThread(@Param("topic") String topic);
    
    @Query("SELECT p.partitionId, COUNT(p) FROM ProcessedMessage p WHERE p.topic = :topic GROUP BY p.partitionId")
    List<Object[]> getMessageCountByPartition(@Param("topic") String topic);
}
