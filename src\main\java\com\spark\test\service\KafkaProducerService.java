package com.spark.test.service;

import com.spark.test.model.OrderEvent;
import com.spark.test.model.PaymentEvent;
import com.spark.test.model.UserEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;

@Service
@RequiredArgsConstructor
@Slf4j
public class KafkaProducerService {

    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    @Value("${kafka.topics.order-events.name}")
    private String orderEventsTopicName;
    
    @Value("${kafka.topics.user-events.name}")
    private String userEventsTopicName;
    
    @Value("${kafka.topics.payment-events.name}")
    private String paymentEventsTopicName;

    private final AtomicLong messageCounter = new AtomicLong(0);

    public CompletableFuture<SendResult<String, Object>> sendOrderEvent(OrderEvent orderEvent) {
        String key = orderEvent.getOrderId();
        return kafkaTemplate.send(orderEventsTopicName, key, orderEvent);
    }

    public CompletableFuture<SendResult<String, Object>> sendUserEvent(UserEvent userEvent) {
        String key = userEvent.getUserId();
        return kafkaTemplate.send(userEventsTopicName, key, userEvent);
    }

    public CompletableFuture<SendResult<String, Object>> sendPaymentEvent(PaymentEvent paymentEvent) {
        String key = paymentEvent.getPaymentId();
        return kafkaTemplate.send(paymentEventsTopicName, key, paymentEvent);
    }

    // Bulk message generation for performance testing
    public List<CompletableFuture<SendResult<String, Object>>> sendBulkOrderEvents(int count) {
        List<CompletableFuture<SendResult<String, Object>>> futures = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            OrderEvent event = generateRandomOrderEvent();
            futures.add(sendOrderEvent(event));
        }
        
        log.info("Sent {} order events", count);
        return futures;
    }

    public List<CompletableFuture<SendResult<String, Object>>> sendBulkUserEvents(int count) {
        List<CompletableFuture<SendResult<String, Object>>> futures = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            UserEvent event = generateRandomUserEvent();
            futures.add(sendUserEvent(event));
        }
        
        log.info("Sent {} user events", count);
        return futures;
    }

    public List<CompletableFuture<SendResult<String, Object>>> sendBulkPaymentEvents(int count) {
        List<CompletableFuture<SendResult<String, Object>>> futures = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            PaymentEvent event = generateRandomPaymentEvent();
            futures.add(sendPaymentEvent(event));
        }
        
        log.info("Sent {} payment events", count);
        return futures;
    }

    private OrderEvent generateRandomOrderEvent() {
        long counter = messageCounter.incrementAndGet();
        Random random = new Random();
        
        List<OrderEvent.OrderItem> items = Arrays.asList(
            new OrderEvent.OrderItem(
                "product-" + random.nextInt(1000),
                "Product " + random.nextInt(1000),
                random.nextInt(5) + 1,
                BigDecimal.valueOf(random.nextDouble() * 100),
                "Category-" + random.nextInt(10)
            )
        );

        return new OrderEvent(
            "order-" + counter,
            "customer-" + random.nextInt(10000),
            "CREATED",
            BigDecimal.valueOf(random.nextDouble() * 1000),
            "USD",
            items,
            LocalDateTime.now(),
            "CREATED",
            "performance-test",
            counter
        );
    }

    private UserEvent generateRandomUserEvent() {
        long counter = messageCounter.incrementAndGet();
        Random random = new Random();
        
        String[] actions = {"LOGIN", "LOGOUT", "VIEW_PRODUCT", "UPDATE_PROFILE"};
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("browser", "Chrome");
        metadata.put("os", "Windows");
        metadata.put("device", "Desktop");

        return new UserEvent(
            "user-" + random.nextInt(10000),
            "user" + random.nextInt(10000) + "@example.com",
            actions[random.nextInt(actions.length)],
            "session-" + counter,
            "192.168.1." + random.nextInt(255),
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            metadata,
            LocalDateTime.now(),
            "USER_ACTION",
            "performance-test",
            counter
        );
    }

    private PaymentEvent generateRandomPaymentEvent() {
        long counter = messageCounter.incrementAndGet();
        Random random = new Random();
        
        String[] methods = {"CREDIT_CARD", "DEBIT_CARD", "PAYPAL", "BANK_TRANSFER"};
        String[] statuses = {"PENDING", "PROCESSING", "COMPLETED", "FAILED"};

        return new PaymentEvent(
            "payment-" + counter,
            "order-" + random.nextInt(10000),
            "customer-" + random.nextInt(10000),
            BigDecimal.valueOf(random.nextDouble() * 1000),
            "USD",
            methods[random.nextInt(methods.length)],
            statuses[random.nextInt(statuses.length)],
            "gateway-txn-" + counter,
            "Success",
            LocalDateTime.now(),
            "PAYMENT_INITIATED",
            "performance-test",
            counter
        );
    }
}
