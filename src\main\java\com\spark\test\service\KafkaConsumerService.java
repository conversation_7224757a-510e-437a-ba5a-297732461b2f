package com.spark.test.service;

import com.spark.test.entity.ProcessedMessage;
import com.spark.test.model.OrderEvent;
import com.spark.test.model.PaymentEvent;
import com.spark.test.model.UserEvent;
import com.spark.test.repository.ProcessedMessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

@Service
@RequiredArgsConstructor
@Slf4j
public class KafkaConsumerService {

    private final ProcessedMessageRepository processedMessageRepository;
    private final PerformanceMonitoringService performanceMonitoringService;
    
    private final AtomicLong processedOrderCount = new AtomicLong(0);
    private final AtomicLong processedUserCount = new AtomicLong(0);
    private final AtomicLong processedPaymentCount = new AtomicLong(0);

    @KafkaListener(topics = "${kafka.topics.order-events.name}",
                   groupId = "${spring.kafka.consumer.group-id}",
                   containerFactory = "kafkaListenerContainerFactory")
    @Transactional
    public void consumeOrderEvents(
            List<ConsumerRecord<String, OrderEvent>> records,
            Acknowledgment acknowledgment) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            String topic = records.isEmpty() ? "order-events" : records.get(0).topic();
            for (ConsumerRecord<String, OrderEvent> record : records) {
                processOrderEvent(record, topic);
            }

            long processingTime = System.currentTimeMillis() - startTime;
            performanceMonitoringService.recordBatchProcessing(topic, records.size(), processingTime);
            
            acknowledgment.acknowledge();
            
            long totalProcessed = processedOrderCount.addAndGet(records.size());
            if (totalProcessed % 1000 == 0) {
                log.info("Processed {} order events so far", totalProcessed);
            }
            
        } catch (Exception e) {
            log.error("Error processing order events batch", e);
            // In production, you might want to implement retry logic or dead letter queue
        }
    }

    @KafkaListener(topics = "${kafka.topics.user-events.name}",
                   groupId = "${spring.kafka.consumer.group-id}",
                   containerFactory = "kafkaListenerContainerFactory")
    @Transactional
    public void consumeUserEvents(
            List<ConsumerRecord<String, UserEvent>> records,
            Acknowledgment acknowledgment) {

        long startTime = System.currentTimeMillis();

        try {
            String topic = records.isEmpty() ? "user-events" : records.get(0).topic();
            for (ConsumerRecord<String, UserEvent> record : records) {
                processUserEvent(record, topic);
            }

            long processingTime = System.currentTimeMillis() - startTime;
            performanceMonitoringService.recordBatchProcessing(topic, records.size(), processingTime);
            
            acknowledgment.acknowledge();
            
            long totalProcessed = processedUserCount.addAndGet(records.size());
            if (totalProcessed % 1000 == 0) {
                log.info("Processed {} user events so far", totalProcessed);
            }
            
        } catch (Exception e) {
            log.error("Error processing user events batch", e);
        }
    }

    @KafkaListener(topics = "${kafka.topics.payment-events.name}",
                   groupId = "${spring.kafka.consumer.group-id}",
                   containerFactory = "kafkaListenerContainerFactory")
    @Transactional
    public void consumePaymentEvents(
            List<ConsumerRecord<String, PaymentEvent>> records,
            Acknowledgment acknowledgment) {

        long startTime = System.currentTimeMillis();

        try {
            String topic = records.isEmpty() ? "payment-events" : records.get(0).topic();
            for (ConsumerRecord<String, PaymentEvent> record : records) {
                processPaymentEvent(record, topic);
            }

            long processingTime = System.currentTimeMillis() - startTime;
            performanceMonitoringService.recordBatchProcessing(topic, records.size(), processingTime);
            
            acknowledgment.acknowledge();
            
            long totalProcessed = processedPaymentCount.addAndGet(records.size());
            if (totalProcessed % 1000 == 0) {
                log.info("Processed {} payment events so far", totalProcessed);
            }
            
        } catch (Exception e) {
            log.error("Error processing payment events batch", e);
        }
    }

    private void processOrderEvent(ConsumerRecord<String, OrderEvent> record, String topic) {
        long startTime = System.currentTimeMillis();
        
        try {
            OrderEvent orderEvent = record.value();
            
            // Simulate business logic processing
            simulateProcessing(orderEvent.getEventType());
            
            // Save processing record
            ProcessedMessage processedMessage = createProcessedMessage(
                record, topic, orderEvent.getEventType(), startTime
            );
            processedMessageRepository.save(processedMessage);
            
        } catch (Exception e) {
            log.error("Error processing order event: {}", record.key(), e);
            saveErrorRecord(record, topic, e.getMessage(), startTime);
        }
    }

    private void processUserEvent(ConsumerRecord<String, UserEvent> record, String topic) {
        long startTime = System.currentTimeMillis();
        
        try {
            UserEvent userEvent = record.value();
            
            // Simulate business logic processing
            simulateProcessing(userEvent.getAction());
            
            // Save processing record
            ProcessedMessage processedMessage = createProcessedMessage(
                record, topic, userEvent.getAction(), startTime
            );
            processedMessageRepository.save(processedMessage);
            
        } catch (Exception e) {
            log.error("Error processing user event: {}", record.key(), e);
            saveErrorRecord(record, topic, e.getMessage(), startTime);
        }
    }

    private void processPaymentEvent(ConsumerRecord<String, PaymentEvent> record, String topic) {
        long startTime = System.currentTimeMillis();
        
        try {
            PaymentEvent paymentEvent = record.value();
            
            // Simulate business logic processing
            simulateProcessing(paymentEvent.getEventType());
            
            // Save processing record
            ProcessedMessage processedMessage = createProcessedMessage(
                record, topic, paymentEvent.getEventType(), startTime
            );
            processedMessageRepository.save(processedMessage);
            
        } catch (Exception e) {
            log.error("Error processing payment event: {}", record.key(), e);
            saveErrorRecord(record, topic, e.getMessage(), startTime);
        }
    }

    private void simulateProcessing(String eventType) {
        // Simulate different processing times based on event type
        try {
            switch (eventType) {
                case "CREATED", "INITIATED" -> Thread.sleep(10); // Fast processing
                case "PROCESSING", "LOGIN" -> Thread.sleep(20); // Medium processing
                case "COMPLETED", "UPDATE_PROFILE" -> Thread.sleep(50); // Slow processing
                default -> Thread.sleep(5); // Default fast processing
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private ProcessedMessage createProcessedMessage(
            ConsumerRecord<String, ?> record, 
            String topic, 
            String eventType, 
            long startTime) {
        
        ProcessedMessage processedMessage = new ProcessedMessage();
        processedMessage.setMessageId(record.key());
        processedMessage.setTopic(topic);
        processedMessage.setPartitionId(record.partition());
        processedMessage.setOffsetValue(record.offset());
        processedMessage.setEventType(eventType);
        processedMessage.setProcessingTimeMs(System.currentTimeMillis() - startTime);
        processedMessage.setThreadName(Thread.currentThread().getName());
        processedMessage.setConsumerGroup("performance-test-group");
        processedMessage.setMessageSize(record.serializedValueSize());
        processedMessage.setStatus("SUCCESS");
        
        return processedMessage;
    }

    private void saveErrorRecord(ConsumerRecord<String, ?> record, String topic, String errorMessage, long startTime) {
        try {
            ProcessedMessage processedMessage = new ProcessedMessage();
            processedMessage.setMessageId(record.key());
            processedMessage.setTopic(topic);
            processedMessage.setPartitionId(record.partition());
            processedMessage.setOffsetValue(record.offset());
            processedMessage.setProcessingTimeMs(System.currentTimeMillis() - startTime);
            processedMessage.setThreadName(Thread.currentThread().getName());
            processedMessage.setConsumerGroup("performance-test-group");
            processedMessage.setMessageSize(record.serializedValueSize());
            processedMessage.setStatus("FAILED");
            processedMessage.setErrorMessage(errorMessage);
            
            processedMessageRepository.save(processedMessage);
        } catch (Exception e) {
            log.error("Failed to save error record", e);
        }
    }

    public long getProcessedOrderCount() {
        return processedOrderCount.get();
    }

    public long getProcessedUserCount() {
        return processedUserCount.get();
    }

    public long getProcessedPaymentCount() {
        return processedPaymentCount.get();
    }

    public void resetCounters() {
        processedOrderCount.set(0);
        processedUserCount.set(0);
        processedPaymentCount.set(0);
    }
}
