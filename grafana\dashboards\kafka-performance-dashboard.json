{"dashboard": {"id": null, "title": "Kafka Performance Monitoring", "tags": ["kafka", "spring-boot", "performance"], "timezone": "browser", "panels": [{"id": 1, "title": "Messages Processed Rate", "type": "stat", "targets": [{"expr": "rate(kafka_messages_processed_total[5m])", "legendFormat": "Messages/sec"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Message Processing Time", "type": "timeseries", "targets": [{"expr": "kafka_message_processing_time_seconds", "legendFormat": "Processing Time"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "JVM Memory Usage", "type": "timeseries", "targets": [{"expr": "jvm_memory_used_bytes{area=\"heap\"}", "legendFormat": "Heap Used"}, {"expr": "jvm_memory_max_bytes{area=\"heap\"}", "legendFormat": "Heap Max"}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "HTTP Request Rate", "type": "timeseries", "targets": [{"expr": "rate(http_server_requests_seconds_count[5m])", "legendFormat": "{{method}} {{uri}}"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}}