# Kafka Performance Testing với Spring Boot

Dự án này được thiết kế để test hiệu năng của Apache Kafka với Spring Boot, PostgreSQL và các kỹ thuật xử lý đa luồng.

## Tính năng chính

- **Multiple Topics**: 3 topics với số partitions khác nhau
  - `order-events`: 6 partitions
  - `user-events`: 4 partitions  
  - `payment-events`: 8 partitions

- **Multi-threaded Processing**: Consumer với 4 threads đồng thời
- **Performance Monitoring**: Metrics chi tiết về throughput và latency
- **Database Integration**: PostgreSQL để lưu trữ processed messages
- **REST APIs**: Endpoints để chạy performance tests
- **Prometheus Metrics**: Monitoring với Micrometer

## Cấu trúc dự án

```
src/main/java/com/spark/test/
├── config/
│   └── KafkaConfig.java              # Kafka configuration
├── controller/
│   └── PerformanceTestController.java # REST endpoints
├── entity/
│   ├── ProcessedMessage.java         # Database entity
│   └── PerformanceMetric.java        # Performance metrics entity
├── model/
│   ├── OrderEvent.java               # Order event model
│   ├── UserEvent.java                # User event model
│   └── PaymentEvent.java             # Payment event model
├── repository/
│   ├── ProcessedMessageRepository.java
│   └── PerformanceMetricRepository.java
├── service/
│   ├── KafkaProducerService.java     # Message producer
│   ├── KafkaConsumerService.java     # Message consumer
│   ├── PerformanceMonitoringService.java
│   └── PerformanceTestService.java   # Test orchestration
└── TestApplication.java
```

## Cách chạy dự án

### 1. Khởi động infrastructure

```bash
# Khởi động Docker containers
docker-compose up -d

# Kiểm tra containers đang chạy
docker-compose ps
```

### 2. Build và chạy ứng dụng

```bash
# Build project
./gradlew build

# Chạy ứng dụng
./gradlew bootRun
```

### 3. Kiểm tra health

```bash
curl http://localhost:8081/api/performance/health
```

## API Endpoints

### Performance Tests

#### Test Order Events
```bash
# Test với 10,000 messages và 4 producer threads
POST http://localhost:8081/api/performance/test/order-events?totalMessages=10000&producerThreads=4
```

#### Test User Events
```bash
# Test với 5,000 messages và 2 producer threads
POST http://localhost:8081/api/performance/test/user-events?totalMessages=5000&producerThreads=2
```

#### Test Payment Events
```bash
# Test với 20,000 messages và 8 producer threads
POST http://localhost:8081/api/performance/test/payment-events?totalMessages=20000&producerThreads=8
```

#### Test Mixed Events
```bash
# Test tất cả loại events cùng lúc
POST http://localhost:8081/api/performance/test/mixed-events?totalMessages=30000&producerThreads=6
```

### Manual Message Sending

```bash
# Gửi 1000 order events
POST http://localhost:8080/api/performance/send/order-events?count=1000

# Gửi 500 user events
POST http://localhost:8080/api/performance/send/user-events?count=500

# Gửi 2000 payment events
POST http://localhost:8080/api/performance/send/payment-events?count=2000
```

### Monitoring & Stats

```bash
# Xem consumer statistics
GET http://localhost:8080/api/performance/stats/consumer

# Xem stats của topic cụ thể
GET http://localhost:8080/api/performance/stats/topic/order-events

# Xem performance metrics history
GET http://localhost:8080/api/performance/metrics/history

# Xem tổng quan metrics
GET http://localhost:8080/api/performance/metrics/summary

# Reset counters
POST http://localhost:8080/api/performance/reset
```

## Kỹ thuật tối ưu hiệu năng

### 1. Topic Partitioning Strategy

- **Order Events**: 6 partitions - phân tán theo `orderId`
- **User Events**: 4 partitions - phân tán theo `userId`  
- **Payment Events**: 8 partitions - phân tán theo `paymentId`

### 2. Consumer Configuration

```yaml
spring:
  kafka:
    consumer:
      max-poll-records: 500      # Batch size lớn
      fetch-min-size: 1024       # Tối thiểu data mỗi lần fetch
      fetch-max-wait: 500        # Timeout cho fetch
    listener:
      ack-mode: batch            # Batch acknowledgment
      concurrency: 4             # 4 consumer threads
      poll-timeout: 3000         # Poll timeout
```

### 3. Producer Optimization

```yaml
spring:
  kafka:
    producer:
      acks: 1                    # Leader acknowledgment only
      batch-size: 16384          # Batch messages
      linger-ms: 5               # Wait time for batching
      compression-type: snappy   # Message compression
```

## Monitoring với Prometheus

Ứng dụng expose metrics tại: `http://localhost:8080/actuator/prometheus`

Key metrics:
- `kafka_messages_processed_total`: Tổng số messages đã xử lý
- `kafka_message_processing_time`: Thời gian xử lý message
- `jvm_memory_used_bytes`: Memory usage
- `jvm_threads_live_threads`: Thread count

## Database Schema

### processed_messages table
```sql
CREATE TABLE processed_messages (
    id BIGSERIAL PRIMARY KEY,
    message_id VARCHAR UNIQUE NOT NULL,
    topic VARCHAR NOT NULL,
    partition_id INTEGER,
    offset_value BIGINT,
    event_type VARCHAR,
    processing_time_ms BIGINT,
    thread_name VARCHAR,
    consumer_group VARCHAR,
    processed_at TIMESTAMP NOT NULL,
    message_size INTEGER,
    status VARCHAR,
    error_message VARCHAR(1000)
);
```

### performance_metrics table
```sql
CREATE TABLE performance_metrics (
    id BIGSERIAL PRIMARY KEY,
    test_name VARCHAR NOT NULL,
    topic VARCHAR NOT NULL,
    total_messages BIGINT,
    messages_per_second DOUBLE PRECISION,
    avg_processing_time_ms DOUBLE PRECISION,
    min_processing_time_ms BIGINT,
    max_processing_time_ms BIGINT,
    total_duration_ms BIGINT,
    thread_count INTEGER,
    partition_count INTEGER,
    batch_size INTEGER,
    success_count BIGINT,
    error_count BIGINT,
    created_at TIMESTAMP NOT NULL
);
```

## Các kịch bản test hiệu năng

### 1. Throughput Test
```bash
# Test với số lượng message lớn
curl -X POST "http://localhost:8080/api/performance/test/order-events?totalMessages=100000&producerThreads=8"
```

### 2. Latency Test
```bash
# Test với batch size nhỏ để đo latency
curl -X POST "http://localhost:8080/api/performance/test/user-events?totalMessages=1000&producerThreads=1"
```

### 3. Load Test
```bash
# Test tải cao với mixed events
curl -X POST "http://localhost:8080/api/performance/test/mixed-events?totalMessages=500000&producerThreads=12"
```

## Tips tối ưu hiệu năng

1. **Partition Strategy**: Chọn partition key phù hợp để phân tán đều
2. **Consumer Threads**: Số threads = số partitions để tối ưu
3. **Batch Processing**: Tăng batch size để giảm overhead
4. **Memory Tuning**: Tăng heap size cho JVM khi test với data lớn
5. **Network**: Sử dụng compression để giảm network overhead

## Troubleshooting

### Kafka Connection Issues
```bash
# Kiểm tra Kafka containers
docker logs kafka
docker logs zookeeper
```

### Database Connection Issues  
```bash
# Kiểm tra PostgreSQL
docker logs postgres
```

### Performance Issues
- Kiểm tra JVM memory usage
- Monitor CPU usage
- Kiểm tra Kafka lag
- Review database query performance
