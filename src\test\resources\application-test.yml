spring:
  application:
    name: kafka-performance-test
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    database-platform: org.hibernate.dialect.H2Dialect
  
  kafka:
    bootstrap-servers: ${spring.embedded.kafka.brokers:localhost:29092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    consumer:
      group-id: test-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest
      properties:
        spring.json.trusted.packages: "com.spark.test.model"

# Test configuration
kafka:
  topics:
    order-events:
      name: test-order-events
      partitions: 2
      replication-factor: 1
    user-events:
      name: test-user-events
      partitions: 2
      replication-factor: 1
    payment-events:
      name: test-payment-events
      partitions: 2
      replication-factor: 1

performance:
  batch-size: 10
  thread-pool-size: 2
  max-messages-per-test: 100

logging:
  level:
    com.spark.test: DEBUG
    org.springframework.kafka: WARN
    org.apache.kafka: WARN
