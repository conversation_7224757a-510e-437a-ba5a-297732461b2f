package com.spark.test.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserEvent {
    private String userId;
    private String email;
    private String action; // LOGIN, LOGOUT, REGISTER, UPDATE_PROFILE, VIEW_PRODUCT
    private String sessionId;
    private String ipAddress;
    private String userAgent;
    private Map<String, Object> metadata;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    private String eventType;
    private String source;
    private Long version;
}
