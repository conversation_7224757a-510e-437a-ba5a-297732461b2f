package com.spark.test.repository;

import com.spark.test.entity.PerformanceMetric;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PerformanceMetricRepository extends JpaRepository<PerformanceMetric, Long> {
    
    List<PerformanceMetric> findByTopicOrderByCreatedAtDesc(String topic);
    
    List<PerformanceMetric> findByTestNameOrderByCreatedAtDesc(String testName);
    
    @Query("SELECT p FROM PerformanceMetric p WHERE p.createdAt BETWEEN :startTime AND :endTime ORDER BY p.createdAt DESC")
    List<PerformanceMetric> findByCreatedAtBetween(
            @Param("startTime") LocalDateTime startTime, 
            @Param("endTime") LocalDateTime endTime
    );
    
    @Query("SELECT AVG(p.messagesPerSecond) FROM PerformanceMetric p WHERE p.topic = :topic")
    Double getAverageMessagesPerSecondByTopic(@Param("topic") String topic);
}
