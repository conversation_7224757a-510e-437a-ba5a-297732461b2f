# Run application in standalone mode (without Docker dependencies)
# This mode uses H2 in-memory database and can run without Kafka

Write-Host "=== Starting Kafka Performance Test (Standalone Mode) ===" -ForegroundColor Green

# Check if Java is available
Write-Host "`nChecking Java..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Host "✓ Java found: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Java not found. Please install Java 17+" -ForegroundColor Red
    exit 1
}

# Set JAVA_HOME
$env:JAVA_HOME = "C:\Program Files\Java\jdk-17"
Write-Host "✓ JAVA_HOME set to: $env:JAVA_HOME" -ForegroundColor Green

# Build the application
Write-Host "`nBuilding application..." -ForegroundColor Yellow
try {
    $buildResult = & ./gradlew build -x test 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Build successful" -ForegroundColor Green
    } else {
        Write-Host "✗ Build failed" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Build error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Start the application in standalone mode
Write-Host "`nStarting application in standalone mode..." -ForegroundColor Yellow
Write-Host "Profile: standalone" -ForegroundColor Gray
Write-Host "Port: 8082" -ForegroundColor Gray
Write-Host "Database: H2 (in-memory)" -ForegroundColor Gray

Write-Host "`nPress Ctrl+C to stop the application" -ForegroundColor Yellow
Write-Host "Application will be available at: http://localhost:8082" -ForegroundColor Cyan

# Run with standalone profile
& ./gradlew bootRun --args="--spring.profiles.active=standalone"
