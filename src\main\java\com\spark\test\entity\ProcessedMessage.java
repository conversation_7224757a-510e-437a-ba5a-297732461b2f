package com.spark.test.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "processed_messages")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessedMessage {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "message_id", unique = true, nullable = false)
    private String messageId;
    
    @Column(name = "topic", nullable = false)
    private String topic;
    
    @Column(name = "partition_id")
    private Integer partitionId;
    
    @Column(name = "offset_value")
    private Long offsetValue;
    
    @Column(name = "event_type")
    private String eventType;
    
    @Column(name = "processing_time_ms")
    private Long processingTimeMs;
    
    @Column(name = "thread_name")
    private String threadName;
    
    @Column(name = "consumer_group")
    private String consumerGroup;
    
    @Column(name = "processed_at", nullable = false)
    private LocalDateTime processedAt;
    
    @Column(name = "message_size")
    private Integer messageSize;
    
    @Column(name = "status")
    private String status; // SUCCESS, FAILED, RETRY
    
    @Column(name = "error_message", length = 1000)
    private String errorMessage;
    
    @PrePersist
    protected void onCreate() {
        processedAt = LocalDateTime.now();
    }
}
