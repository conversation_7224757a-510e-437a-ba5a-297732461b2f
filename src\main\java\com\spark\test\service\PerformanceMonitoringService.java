package com.spark.test.service;

import com.spark.test.entity.PerformanceMetric;
import com.spark.test.repository.PerformanceMetricRepository;
import com.spark.test.repository.ProcessedMessageRepository;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Service
@RequiredArgsConstructor
@Slf4j
public class PerformanceMonitoringService {

    private final PerformanceMetricRepository performanceMetricRepository;
    private final ProcessedMessageRepository processedMessageRepository;
    private final MeterRegistry meterRegistry;

    // Metrics tracking
    private final ConcurrentHashMap<String, AtomicLong> messageCounters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> processingTimes = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, LocalDateTime> testStartTimes = new ConcurrentHashMap<>();

    // Micrometer metrics
    private final Counter messagesProcessedCounter;
    private final Timer messageProcessingTimer;

    public PerformanceMonitoringService(
            PerformanceMetricRepository performanceMetricRepository,
            ProcessedMessageRepository processedMessageRepository,
            MeterRegistry meterRegistry) {
        this.performanceMetricRepository = performanceMetricRepository;
        this.processedMessageRepository = processedMessageRepository;
        this.meterRegistry = meterRegistry;
        
        this.messagesProcessedCounter = Counter.builder("kafka.messages.processed")
                .description("Total number of processed messages")
                .register(meterRegistry);
                
        this.messageProcessingTimer = Timer.builder("kafka.message.processing.time")
                .description("Message processing time")
                .register(meterRegistry);
    }

    public void startPerformanceTest(String testName, String topic) {
        testStartTimes.put(testName + ":" + topic, LocalDateTime.now());
        messageCounters.put(testName + ":" + topic, new AtomicLong(0));
        processingTimes.put(testName + ":" + topic, new AtomicLong(0));
        
        log.info("Started performance test: {} for topic: {}", testName, topic);
    }

    public void recordBatchProcessing(String topic, int batchSize, long processingTimeMs) {
        // Update Micrometer metrics
        messagesProcessedCounter.increment(batchSize);
        messageProcessingTimer.record(processingTimeMs, java.util.concurrent.TimeUnit.MILLISECONDS);
        
        // Update internal counters for all active tests
        messageCounters.entrySet().stream()
                .filter(entry -> entry.getKey().endsWith(":" + topic))
                .forEach(entry -> {
                    entry.getValue().addAndGet(batchSize);
                    processingTimes.get(entry.getKey()).addAndGet(processingTimeMs);
                });
    }

    public PerformanceMetric endPerformanceTest(String testName, String topic, int threadCount, int partitionCount, int batchSize) {
        String key = testName + ":" + topic;
        LocalDateTime startTime = testStartTimes.get(key);
        LocalDateTime endTime = LocalDateTime.now();
        
        if (startTime == null) {
            log.warn("No start time found for test: {} topic: {}", testName, topic);
            return null;
        }

        long totalMessages = messageCounters.getOrDefault(key, new AtomicLong(0)).get();
        long totalProcessingTime = processingTimes.getOrDefault(key, new AtomicLong(0)).get();
        long totalDurationMs = java.time.Duration.between(startTime, endTime).toMillis();

        // Calculate metrics
        double messagesPerSecond = totalDurationMs > 0 ? (totalMessages * 1000.0) / totalDurationMs : 0;
        double avgProcessingTimeMs = totalMessages > 0 ? (double) totalProcessingTime / totalMessages : 0;

        // Get detailed stats from database
        Double dbAvgProcessingTime = processedMessageRepository.getAverageProcessingTimeByTopic(topic);
        Long successCount = processedMessageRepository.countByTopicAndStatus(topic, "SUCCESS");
        Long errorCount = processedMessageRepository.countByTopicAndStatus(topic, "FAILED");

        // Create performance metric record
        PerformanceMetric metric = new PerformanceMetric();
        metric.setTestName(testName);
        metric.setTopic(topic);
        metric.setTotalMessages(totalMessages);
        metric.setMessagesPerSecond(messagesPerSecond);
        metric.setAvgProcessingTimeMs(dbAvgProcessingTime != null ? dbAvgProcessingTime : avgProcessingTimeMs);
        metric.setTotalDurationMs(totalDurationMs);
        metric.setThreadCount(threadCount);
        metric.setPartitionCount(partitionCount);
        metric.setBatchSize(batchSize);
        metric.setSuccessCount(successCount != null ? successCount : 0);
        metric.setErrorCount(errorCount != null ? errorCount : 0);

        // Calculate min/max processing times (simplified - you might want to track these more precisely)
        metric.setMinProcessingTimeMs(1L); // Placeholder
        metric.setMaxProcessingTimeMs(100L); // Placeholder

        PerformanceMetric savedMetric = performanceMetricRepository.save(metric);

        // Clean up tracking data
        testStartTimes.remove(key);
        messageCounters.remove(key);
        processingTimes.remove(key);

        log.info("Performance test completed: {} - {} messages/sec, {} total messages, {} ms total duration",
                testName, String.format("%.2f", messagesPerSecond), totalMessages, totalDurationMs);

        return savedMetric;
    }

    public void recordCustomMetric(String metricName, String topic, double value) {
        meterRegistry.gauge("kafka.custom." + metricName, "topic", topic, value);
    }

    public PerformanceTestSummary getTestSummary(String topic) {
        Long successCount = processedMessageRepository.countByTopicAndStatus(topic, "SUCCESS");
        Long errorCount = processedMessageRepository.countByTopicAndStatus(topic, "FAILED");
        Double avgProcessingTime = processedMessageRepository.getAverageProcessingTimeByTopic(topic);

        return new PerformanceTestSummary(
                topic,
                successCount != null ? successCount : 0,
                errorCount != null ? errorCount : 0,
                avgProcessingTime != null ? avgProcessingTime : 0.0
        );
    }

    public static class PerformanceTestSummary {
        public final String topic;
        public final long successCount;
        public final long errorCount;
        public final double avgProcessingTimeMs;

        public PerformanceTestSummary(String topic, long successCount, long errorCount, double avgProcessingTimeMs) {
            this.topic = topic;
            this.successCount = successCount;
            this.errorCount = errorCount;
            this.avgProcessingTimeMs = avgProcessingTimeMs;
        }
    }
}
