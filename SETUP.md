# Hướng dẫn Setup và Chạy Dự án Kafka Performance Testing

## Y<PERSON><PERSON> cầu hệ thống

- **Java 17+** (đã có)
- **Docker Desktop** (cần cài đặt và chạy)
- **PowerShell** (Windows)
- **Git** (để clone project)

## Bước 1: Cài đặt Docker Desktop

1. Tải Docker Desktop từ: https://www.docker.com/products/docker-desktop/
2. Cài đặt và khởi động Docker Desktop
3. Đảm bảo Docker đang chạy (icon Docker ở system tray)

## Bước 2: Khởi động Infrastructure

```bash
# Mở PowerShell với quyền Administrator
# Di chuyển đến thư mục dự án
cd D:\projec-tax\test

# Khởi động tất cả services
docker-compose up -d

# Kiểm tra containers đang chạy
docker ps
```

**Chờ khoảng 2-3 phút** để tất cả services khởi động hoàn tất.

## Bước 3: Ki<PERSON>m tra Services

### Kiểm tra Kafka
```bash
# Kiểm tra Kafka logs
docker logs kafka

# Kiểm tra Zookeeper
docker logs zookeeper
```

### Kiểm tra PostgreSQL
```bash
# Kiểm tra PostgreSQL logs
docker logs postgres
```

### Kiểm tra tất cả services
```bash
# Xem status của tất cả containers
docker-compose ps
```

## Bước 4: Chạy Spring Boot Application

```bash
# Mở PowerShell mới (không cần admin)
cd D:\projec-tax\test

# Set JAVA_HOME và chạy ứng dụng
$env:JAVA_HOME = "C:\Program Files\Java\jdk-17"
./gradlew bootRun
```

**Chờ ứng dụng khởi động** (khoảng 30-60 giây)

## Bước 5: Kiểm tra Application

```bash
# Mở PowerShell mới
# Test health endpoint
Invoke-RestMethod -Uri "http://localhost:8081/api/performance/health" -Method Get
```

Kết quả mong đợi:
```json
{
  "service": "Kafka Performance Test Service",
  "status": "UP",
  "timestamp": "2025-06-04T11:23:41.*********"
}
```

## Bước 6: Chạy Performance Tests

### Test đơn giản
```bash
# Gửi 5 order events
Invoke-RestMethod -Uri "http://localhost:8081/api/performance/send/order-events?count=5" -Method Post

# Kiểm tra consumer stats
Invoke-RestMethod -Uri "http://localhost:8081/api/performance/stats/consumer" -Method Get
```

### Chạy Performance Test Script
```bash
# Chạy script tự động
.\test-performance.ps1
```

## Troubleshooting

### 1. Docker không chạy
```bash
# Khởi động Docker Desktop
# Chờ Docker khởi động hoàn tất
# Chạy lại: docker-compose up -d
```

### 2. Port đã được sử dụng
```bash
# Kiểm tra process đang dùng port
netstat -ano | findstr :8081
netstat -ano | findstr :29092
netstat -ano | findstr :15432

# Kill process nếu cần
taskkill /PID <PID_NUMBER> /F
```

### 3. Kafka connection timeout
```bash
# Restart Kafka containers
docker-compose restart kafka zookeeper

# Chờ 2-3 phút rồi test lại
```

### 4. Database connection error
```bash
# Restart PostgreSQL
docker-compose restart postgres

# Kiểm tra logs
docker logs postgres
```

### 5. Application không start
```bash
# Kiểm tra Java version
java -version

# Kiểm tra JAVA_HOME
echo $env:JAVA_HOME

# Clean và rebuild
./gradlew clean build
```

## Monitoring URLs

Sau khi tất cả services chạy thành công:

- **Application**: http://localhost:8081/api/performance/health
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)
- **Kibana**: http://localhost:5601
- **Zipkin**: http://localhost:9411

## Test Commands

### Basic Tests
```bash
# Health check
Invoke-RestMethod -Uri "http://localhost:8081/api/performance/health" -Method Get

# Send messages
Invoke-RestMethod -Uri "http://localhost:8081/api/performance/send/order-events?count=10" -Method Post
Invoke-RestMethod -Uri "http://localhost:8081/api/performance/send/user-events?count=5" -Method Post
Invoke-RestMethod -Uri "http://localhost:8081/api/performance/send/payment-events?count=8" -Method Post

# Check stats
Invoke-RestMethod -Uri "http://localhost:8081/api/performance/stats/consumer" -Method Get
```

### Performance Tests
```bash
# Small test
Invoke-RestMethod -Uri "http://localhost:8081/api/performance/test/order-events?totalMessages=100&producerThreads=2" -Method Post

# Medium test
Invoke-RestMethod -Uri "http://localhost:8081/api/performance/test/user-events?totalMessages=1000&producerThreads=4" -Method Post

# Large test (chỉ chạy khi system ổn định)
Invoke-RestMethod -Uri "http://localhost:8081/api/performance/test/mixed-events?totalMessages=10000&producerThreads=6" -Method Post
```

## Shutdown

```bash
# Stop Spring Boot application (Ctrl+C trong terminal đang chạy)

# Stop Docker containers
docker-compose down

# Stop và xóa volumes (nếu muốn reset data)
docker-compose down -v
```

## Logs

### Application Logs
- Xem trong terminal đang chạy `./gradlew bootRun`

### Docker Logs
```bash
docker logs kafka
docker logs postgres
docker logs zookeeper
```

### Database Data
- PostgreSQL: Kết nối qua port 15432
- Credentials: admin/pz;H8UgB
- Database: product_db
