package com.spark.test.service;

import com.spark.test.model.OrderEvent;
import com.spark.test.model.PaymentEvent;
import com.spark.test.model.UserEvent;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(properties = {
    "spring.kafka.bootstrap-servers=localhost:29092",
    "spring.datasource.url=********************************************"
})
class KafkaProducerServiceTest {

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Test
    void testSendBulkOrderEvents() {
        // Given
        int messageCount = 10;

        // When
        List<CompletableFuture<SendResult<String, Object>>> futures = 
            kafkaProducerService.sendBulkOrderEvents(messageCount);

        // Then
        assertNotNull(futures);
        assertEquals(messageCount, futures.size());
        
        // Wait for all messages to be sent
        futures.forEach(future -> {
            try {
                SendResult<String, Object> result = future.get();
                assertNotNull(result);
                assertNotNull(result.getRecordMetadata());
            } catch (Exception e) {
                fail("Failed to send message: " + e.getMessage());
            }
        });
    }

    @Test
    void testSendBulkUserEvents() {
        // Given
        int messageCount = 5;

        // When
        List<CompletableFuture<SendResult<String, Object>>> futures = 
            kafkaProducerService.sendBulkUserEvents(messageCount);

        // Then
        assertNotNull(futures);
        assertEquals(messageCount, futures.size());
    }

    @Test
    void testSendBulkPaymentEvents() {
        // Given
        int messageCount = 8;

        // When
        List<CompletableFuture<SendResult<String, Object>>> futures = 
            kafkaProducerService.sendBulkPaymentEvents(messageCount);

        // Then
        assertNotNull(futures);
        assertEquals(messageCount, futures.size());
    }
}
