package com.spark.test.controller;

import com.spark.test.entity.PerformanceMetric;
import com.spark.test.repository.PerformanceMetricRepository;
import com.spark.test.repository.ProcessedMessageRepository;
import com.spark.test.service.KafkaConsumerService;
import com.spark.test.service.KafkaProducerService;
import com.spark.test.service.PerformanceMonitoringService;
import com.spark.test.service.PerformanceTestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/performance")
@RequiredArgsConstructor
@Slf4j
public class PerformanceTestController {

    private final PerformanceTestService performanceTestService;
    private final KafkaProducerService kafkaProducerService;
    private final KafkaConsumerService kafkaConsumerService;
    private final PerformanceMonitoringService performanceMonitoringService;
    private final PerformanceMetricRepository performanceMetricRepository;
    private final ProcessedMessageRepository processedMessageRepository;

    @PostMapping("/test/order-events")
    public ResponseEntity<PerformanceTestService.PerformanceTestResult> testOrderEvents(
            @RequestParam(defaultValue = "10000") int totalMessages,
            @RequestParam(defaultValue = "4") int producerThreads) {
        
        log.info("Starting order events performance test with {} messages and {} threads", 
                totalMessages, producerThreads);
        
        PerformanceTestService.PerformanceTestResult result = 
            performanceTestService.runOrderEventsPerformanceTest(totalMessages, producerThreads);
        
        return ResponseEntity.ok(result);
    }

    @PostMapping("/test/user-events")
    public ResponseEntity<PerformanceTestService.PerformanceTestResult> testUserEvents(
            @RequestParam(defaultValue = "10000") int totalMessages,
            @RequestParam(defaultValue = "4") int producerThreads) {
        
        log.info("Starting user events performance test with {} messages and {} threads", 
                totalMessages, producerThreads);
        
        PerformanceTestService.PerformanceTestResult result = 
            performanceTestService.runUserEventsPerformanceTest(totalMessages, producerThreads);
        
        return ResponseEntity.ok(result);
    }

    @PostMapping("/test/payment-events")
    public ResponseEntity<PerformanceTestService.PerformanceTestResult> testPaymentEvents(
            @RequestParam(defaultValue = "10000") int totalMessages,
            @RequestParam(defaultValue = "4") int producerThreads) {
        
        log.info("Starting payment events performance test with {} messages and {} threads", 
                totalMessages, producerThreads);
        
        PerformanceTestService.PerformanceTestResult result = 
            performanceTestService.runPaymentEventsPerformanceTest(totalMessages, producerThreads);
        
        return ResponseEntity.ok(result);
    }

    @PostMapping("/test/mixed-events")
    public ResponseEntity<PerformanceTestService.PerformanceTestResult> testMixedEvents(
            @RequestParam(defaultValue = "30000") int totalMessages,
            @RequestParam(defaultValue = "6") int producerThreads) {
        
        log.info("Starting mixed events performance test with {} messages and {} threads", 
                totalMessages, producerThreads);
        
        PerformanceTestService.PerformanceTestResult result = 
            performanceTestService.runMixedEventsPerformanceTest(totalMessages, producerThreads);
        
        return ResponseEntity.ok(result);
    }

    @PostMapping("/send/order-events")
    public ResponseEntity<Map<String, Object>> sendOrderEvents(
            @RequestParam(defaultValue = "1000") int count) {
        
        long startTime = System.currentTimeMillis();
        kafkaProducerService.sendBulkOrderEvents(count);
        long duration = System.currentTimeMillis() - startTime;
        
        Map<String, Object> response = new HashMap<>();
        response.put("messagesSent", count);
        response.put("durationMs", duration);
        response.put("messagesPerSecond", count * 1000.0 / duration);
        response.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(response);
    }

    @PostMapping("/send/user-events")
    public ResponseEntity<Map<String, Object>> sendUserEvents(
            @RequestParam(defaultValue = "1000") int count) {
        
        long startTime = System.currentTimeMillis();
        kafkaProducerService.sendBulkUserEvents(count);
        long duration = System.currentTimeMillis() - startTime;
        
        Map<String, Object> response = new HashMap<>();
        response.put("messagesSent", count);
        response.put("durationMs", duration);
        response.put("messagesPerSecond", count * 1000.0 / duration);
        response.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(response);
    }

    @PostMapping("/send/payment-events")
    public ResponseEntity<Map<String, Object>> sendPaymentEvents(
            @RequestParam(defaultValue = "1000") int count) {
        
        long startTime = System.currentTimeMillis();
        kafkaProducerService.sendBulkPaymentEvents(count);
        long duration = System.currentTimeMillis() - startTime;
        
        Map<String, Object> response = new HashMap<>();
        response.put("messagesSent", count);
        response.put("durationMs", duration);
        response.put("messagesPerSecond", count * 1000.0 / duration);
        response.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/stats/consumer")
    public ResponseEntity<Map<String, Object>> getConsumerStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("processedOrderCount", kafkaConsumerService.getProcessedOrderCount());
        stats.put("processedUserCount", kafkaConsumerService.getProcessedUserCount());
        stats.put("processedPaymentCount", kafkaConsumerService.getProcessedPaymentCount());
        stats.put("totalProcessed", 
            kafkaConsumerService.getProcessedOrderCount() + 
            kafkaConsumerService.getProcessedUserCount() + 
            kafkaConsumerService.getProcessedPaymentCount());
        stats.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/stats/topic/{topic}")
    public ResponseEntity<PerformanceMonitoringService.PerformanceTestSummary> getTopicStats(
            @PathVariable String topic) {
        
        PerformanceMonitoringService.PerformanceTestSummary summary = 
            performanceMonitoringService.getTestSummary(topic);
        
        return ResponseEntity.ok(summary);
    }

    @GetMapping("/metrics/history")
    public ResponseEntity<List<PerformanceMetric>> getMetricsHistory(
            @RequestParam(required = false) String topic,
            @RequestParam(required = false) String testName) {
        
        List<PerformanceMetric> metrics;
        
        if (topic != null) {
            metrics = performanceMetricRepository.findByTopicOrderByCreatedAtDesc(topic);
        } else if (testName != null) {
            metrics = performanceMetricRepository.findByTestNameOrderByCreatedAtDesc(testName);
        } else {
            metrics = performanceMetricRepository.findAll();
        }
        
        return ResponseEntity.ok(metrics);
    }

    @GetMapping("/metrics/summary")
    public ResponseEntity<Map<String, Object>> getMetricsSummary() {
        Map<String, Object> summary = new HashMap<>();
        
        // Get total processed messages
        long totalProcessed = processedMessageRepository.count();
        summary.put("totalProcessedMessages", totalProcessed);
        
        // Get success/error counts
        Long successCount = processedMessageRepository.countByTopicAndStatus("order-events", "SUCCESS") +
                           processedMessageRepository.countByTopicAndStatus("user-events", "SUCCESS") +
                           processedMessageRepository.countByTopicAndStatus("payment-events", "SUCCESS");
        
        Long errorCount = processedMessageRepository.countByTopicAndStatus("order-events", "FAILED") +
                         processedMessageRepository.countByTopicAndStatus("user-events", "FAILED") +
                         processedMessageRepository.countByTopicAndStatus("payment-events", "FAILED");
        
        summary.put("successCount", successCount != null ? successCount : 0);
        summary.put("errorCount", errorCount != null ? errorCount : 0);
        summary.put("successRate", totalProcessed > 0 ? 
            (successCount != null ? successCount : 0) * 100.0 / totalProcessed : 0);
        
        // Get average processing times by topic
        Map<String, Double> avgProcessingTimes = new HashMap<>();
        avgProcessingTimes.put("order-events", 
            processedMessageRepository.getAverageProcessingTimeByTopic("order-events"));
        avgProcessingTimes.put("user-events", 
            processedMessageRepository.getAverageProcessingTimeByTopic("user-events"));
        avgProcessingTimes.put("payment-events", 
            processedMessageRepository.getAverageProcessingTimeByTopic("payment-events"));
        
        summary.put("avgProcessingTimesByTopic", avgProcessingTimes);
        summary.put("timestamp", LocalDateTime.now());
        
        return ResponseEntity.ok(summary);
    }

    @PostMapping("/reset")
    public ResponseEntity<Map<String, String>> resetCounters() {
        kafkaConsumerService.resetCounters();
        
        Map<String, String> response = new HashMap<>();
        response.put("message", "Consumer counters reset successfully");
        response.put("timestamp", LocalDateTime.now().toString());
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> healthCheck() {
        Map<String, String> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "Kafka Performance Test Service");
        health.put("timestamp", LocalDateTime.now().toString());
        
        return ResponseEntity.ok(health);
    }
}
