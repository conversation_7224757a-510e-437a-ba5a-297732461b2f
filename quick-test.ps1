# Quick Test Script - Test application without Docker
# Chỉ test các API endpoints cơ bản

Write-Host "=== Quick Application Test ===" -ForegroundColor Green

# Function to test API endpoint
function Test-Endpoint {
    param($url, $method, $description)
    
    Write-Host "`nTesting: $description" -ForegroundColor Yellow
    Write-Host "URL: $url" -ForegroundColor Gray
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method $method -TimeoutSec 30
        Write-Host "✓ SUCCESS" -ForegroundColor Green
        
        if ($response -is [PSCustomObject]) {
            $response | ConvertTo-Json -Depth 2 | Write-Host -ForegroundColor Cyan
        } else {
            Write-Host $response -ForegroundColor Cyan
        }
        
        return $true
    }
    catch {
        Write-Host "✗ FAILED: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Check if application is running
Write-Host "`n1. Checking if application is running..." -ForegroundColor Yellow

$healthCheck = Test-Endpoint "http://localhost:8081/api/performance/health" "GET" "Health Check"

if (-not $healthCheck) {
    Write-Host "`nApplication is not running!" -ForegroundColor Red
    Write-Host "Please start the application first:" -ForegroundColor Yellow
    Write-Host "  1. Open PowerShell" -ForegroundColor White
    Write-Host "  2. cd D:\projec-tax\test" -ForegroundColor White
    Write-Host "  3. `$env:JAVA_HOME = 'C:\Program Files\Java\jdk-17'" -ForegroundColor White
    Write-Host "  4. ./gradlew bootRun" -ForegroundColor White
    Write-Host "`nThen run this script again." -ForegroundColor Yellow
    exit 1
}

Write-Host "`n2. Testing basic endpoints..." -ForegroundColor Yellow

# Test consumer stats (should work even without Kafka)
Test-Endpoint "http://localhost:8081/api/performance/stats/consumer" "GET" "Consumer Statistics"

# Test metrics summary
Test-Endpoint "http://localhost:8081/api/performance/metrics/summary" "GET" "Metrics Summary"

# Test metrics history
Test-Endpoint "http://localhost:8081/api/performance/metrics/history" "GET" "Metrics History"

Write-Host "`n3. Testing message sending (may fail without Kafka)..." -ForegroundColor Yellow

# These will likely fail without Kafka running, but let's test the endpoints
$sendTests = @(
    @{url="http://localhost:8081/api/performance/send/order-events?count=1"; desc="Send 1 Order Event"},
    @{url="http://localhost:8081/api/performance/send/user-events?count=1"; desc="Send 1 User Event"},
    @{url="http://localhost:8081/api/performance/send/payment-events?count=1"; desc="Send 1 Payment Event"}
)

$successCount = 0
foreach ($test in $sendTests) {
    if (Test-Endpoint $test.url "POST" $test.desc) {
        $successCount++
    }
}

Write-Host "`n4. Testing performance test endpoints (may fail without Kafka)..." -ForegroundColor Yellow

$perfTests = @(
    @{url="http://localhost:8081/api/performance/test/order-events?totalMessages=5&producerThreads=1"; desc="Small Order Events Test"},
    @{url="http://localhost:8081/api/performance/test/user-events?totalMessages=3&producerThreads=1"; desc="Small User Events Test"}
)

$perfSuccessCount = 0
foreach ($test in $perfTests) {
    if (Test-Endpoint $test.url "POST" $test.desc) {
        $perfSuccessCount++
    }
}

# Summary
Write-Host "`n=== Test Summary ===" -ForegroundColor Green
Write-Host "Application Health: ✓ RUNNING" -ForegroundColor Green

if ($successCount -gt 0) {
    Write-Host "Message Sending: ✓ $successCount/3 endpoints working" -ForegroundColor Green
    Write-Host "This means Kafka is likely running correctly!" -ForegroundColor Green
} else {
    Write-Host "Message Sending: ✗ All endpoints failed" -ForegroundColor Red
    Write-Host "This likely means Kafka is not running." -ForegroundColor Yellow
    Write-Host "To start Kafka:" -ForegroundColor Yellow
    Write-Host "  1. Start Docker Desktop" -ForegroundColor White
    Write-Host "  2. Run: docker-compose up -d" -ForegroundColor White
    Write-Host "  3. Wait 2-3 minutes for Kafka to start" -ForegroundColor White
    Write-Host "  4. Run this script again" -ForegroundColor White
}

if ($perfSuccessCount -gt 0) {
    Write-Host "Performance Tests: ✓ $perfSuccessCount/2 tests working" -ForegroundColor Green
} else {
    Write-Host "Performance Tests: ✗ Tests failed (expected without Kafka)" -ForegroundColor Yellow
}

Write-Host "`nNext Steps:" -ForegroundColor Yellow
if ($successCount -eq 3) {
    Write-Host "  • Everything is working! You can run full performance tests" -ForegroundColor Green
    Write-Host "  • Try: .\test-performance.ps1" -ForegroundColor White
    Write-Host "  • Or run larger tests manually" -ForegroundColor White
} else {
    Write-Host "  • Start Docker and Kafka (see SETUP.md)" -ForegroundColor White
    Write-Host "  • Then run: .\test-performance.ps1" -ForegroundColor White
}

Write-Host "`nUseful URLs:" -ForegroundColor Yellow
Write-Host "  • Application: http://localhost:8081/api/performance/health" -ForegroundColor Cyan
Write-Host "  • Prometheus: http://localhost:9090 (if Docker running)" -ForegroundColor Cyan
Write-Host "  • Grafana: http://localhost:3000 (if Docker running)" -ForegroundColor Cyan

Write-Host "`n=== Quick Test Completed ===" -ForegroundColor Green
