global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Spring Boot Application
  - job_name: 'spring-boot-kafka-app'
    static_configs:
      - targets: ['host.docker.internal:8081', 'host.docker.internal:8082']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 5s

  # Kafka Exporter
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka_exporter:9308']
    scrape_interval: 10s

  # PostgreSQL Exporter
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres_exporter:9187']
    scrape_interval: 10s

  # Redis Exporter
  - job_name: 'redis'
    static_configs:
      - targets: ['redis_exporter:9121']
    scrape_interval: 10s

  # Elasticsearch Exporter
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch_exporter:9114']
    scrape_interval: 10s

  # MongoDB Exporter
  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb_exporter:9216']
    scrape_interval: 10s

  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
