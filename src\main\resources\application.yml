spring:
  application:
    name: kafka-performance-test
  
  datasource:
    url: ********************************************
    username: admin
    password: pz;H8UgB
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  kafka:
    bootstrap-servers: localhost:29092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: 1
      retries: 3
      batch-size: 16384
      linger-ms: 5
      buffer-memory: 33554432
      compression-type: snappy
    
    consumer:
      group-id: performance-test-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 500
      fetch-min-size: 1024
      fetch-max-wait: 500
      properties:
        spring.json.trusted.packages: "com.spark.test.model"
    
    listener:
      ack-mode: batch
      concurrency: 4
      poll-timeout: 3000

# Custom Kafka Configuration
kafka:
  topics:
    order-events:
      name: order-events
      partitions: 6
      replication-factor: 1
    user-events:
      name: user-events
      partitions: 4
      replication-factor: 1
    payment-events:
      name: payment-events
      partitions: 8
      replication-factor: 1

# Performance Testing Configuration
performance:
  batch-size: 1000
  thread-pool-size: 10
  max-messages-per-test: 100000

# Actuator for monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.spark.test: INFO
    org.springframework.kafka: WARN
    org.apache.kafka: WARN
