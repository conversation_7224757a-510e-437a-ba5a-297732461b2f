{"dashboard": {"id": null, "title": "Kafka Performance Monitoring", "tags": ["kafka", "spring-boot", "performance"], "timezone": "browser", "panels": [{"id": 1, "title": "Messages Processed Rate", "type": "stat", "targets": [{"expr": "rate(kafka_messages_processed_total[5m])", "legendFormat": "Messages/sec", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Total Messages Processed", "type": "stat", "targets": [{"expr": "kafka_messages_processed_total", "legendFormat": "Total Messages", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Message Processing Time", "type": "timeseries", "targets": [{"expr": "kafka_message_processing_time_seconds_sum / kafka_message_processing_time_seconds_count", "legendFormat": "Avg Processing Time", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 4, "title": "JVM Memory Usage", "type": "timeseries", "targets": [{"expr": "jvm_memory_used_bytes{area=\"heap\"}", "legendFormat": "Heap Used", "refId": "A"}, {"expr": "jvm_memory_max_bytes{area=\"heap\"}", "legendFormat": "Heap Max", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 5, "title": "HTTP Request Rate", "type": "timeseries", "targets": [{"expr": "rate(http_server_requests_seconds_count[5m])", "legendFormat": "{{method}} {{uri}}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqps", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 6, "title": "Kafka Consumer Lag", "type": "timeseries", "targets": [{"expr": "kafka_consumer_lag_sum", "legendFormat": "Consumer Lag", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 7, "title": "Database Connections", "type": "timeseries", "targets": [{"expr": "hikaricp_connections_active", "legendFormat": "Active Connections", "refId": "A"}, {"expr": "hikaricp_connections_idle", "legendFormat": "Idle Connections", "refId": "B"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s"}}