package com.spark.test.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "performance_metrics")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PerformanceMetric {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "test_name", nullable = false)
    private String testName;
    
    @Column(name = "topic", nullable = false)
    private String topic;
    
    @Column(name = "total_messages")
    private Long totalMessages;
    
    @Column(name = "messages_per_second")
    private Double messagesPerSecond;
    
    @Column(name = "avg_processing_time_ms")
    private Double avgProcessingTimeMs;
    
    @Column(name = "min_processing_time_ms")
    private Long minProcessingTimeMs;
    
    @Column(name = "max_processing_time_ms")
    private Long maxProcessingTimeMs;
    
    @Column(name = "total_duration_ms")
    private Long totalDurationMs;
    
    @Column(name = "thread_count")
    private Integer threadCount;
    
    @Column(name = "partition_count")
    private Integer partitionCount;
    
    @Column(name = "batch_size")
    private Integer batchSize;
    
    @Column(name = "success_count")
    private Long successCount;
    
    @Column(name = "error_count")
    private Long errorCount;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
}
