package com.spark.test.service;

import com.spark.test.entity.PerformanceMetric;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class PerformanceTestService {

    private final KafkaProducerService kafkaProducerService;
    private final KafkaConsumerService kafkaConsumerService;
    private final PerformanceMonitoringService performanceMonitoringService;

    @Value("${kafka.topics.order-events.name}")
    private String orderEventsTopicName;

    @Value("${kafka.topics.user-events.name}")
    private String userEventsTopicName;

    @Value("${kafka.topics.payment-events.name}")
    private String paymentEventsTopicName;

    @Value("${kafka.topics.order-events.partitions}")
    private int orderEventsPartitions;

    @Value("${kafka.topics.user-events.partitions}")
    private int userEventsPartitions;

    @Value("${kafka.topics.payment-events.partitions}")
    private int paymentEventsPartitions;

    @Value("${performance.thread-pool-size}")
    private int threadPoolSize;

    @Value("${performance.batch-size}")
    private int batchSize;

    public PerformanceTestResult runOrderEventsPerformanceTest(int totalMessages, int producerThreads) {
        String testName = "OrderEvents_" + totalMessages + "_" + producerThreads + "threads";
        
        log.info("Starting order events performance test: {} messages with {} producer threads", 
                totalMessages, producerThreads);

        // Reset consumer counters
        kafkaConsumerService.resetCounters();
        
        // Start monitoring
        performanceMonitoringService.startPerformanceTest(testName, orderEventsTopicName);

        long startTime = System.currentTimeMillis();

        // Send messages using multiple producer threads
        ExecutorService producerExecutor = Executors.newFixedThreadPool(producerThreads);
        int messagesPerThread = totalMessages / producerThreads;
        
        try {
            for (int i = 0; i < producerThreads; i++) {
                final int threadMessages = (i == producerThreads - 1) ? 
                    messagesPerThread + (totalMessages % producerThreads) : messagesPerThread;
                
                producerExecutor.submit(() -> {
                    kafkaProducerService.sendBulkOrderEvents(threadMessages);
                });
            }

            producerExecutor.shutdown();
            producerExecutor.awaitTermination(5, TimeUnit.MINUTES);

            // Wait for all messages to be consumed
            waitForConsumption(totalMessages, () -> kafkaConsumerService.getProcessedOrderCount());

            long endTime = System.currentTimeMillis();
            long totalDuration = endTime - startTime;

            // End monitoring and get metrics
            PerformanceMetric metric = performanceMonitoringService.endPerformanceTest(
                testName, orderEventsTopicName, 4, orderEventsPartitions, batchSize);

            return new PerformanceTestResult(
                testName,
                orderEventsTopicName,
                totalMessages,
                kafkaConsumerService.getProcessedOrderCount(),
                totalDuration,
                metric
            );

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Performance test interrupted", e);
            return null;
        }
    }

    public PerformanceTestResult runUserEventsPerformanceTest(int totalMessages, int producerThreads) {
        String testName = "UserEvents_" + totalMessages + "_" + producerThreads + "threads";
        
        log.info("Starting user events performance test: {} messages with {} producer threads", 
                totalMessages, producerThreads);

        kafkaConsumerService.resetCounters();
        performanceMonitoringService.startPerformanceTest(testName, userEventsTopicName);

        long startTime = System.currentTimeMillis();

        ExecutorService producerExecutor = Executors.newFixedThreadPool(producerThreads);
        int messagesPerThread = totalMessages / producerThreads;
        
        try {
            for (int i = 0; i < producerThreads; i++) {
                final int threadMessages = (i == producerThreads - 1) ? 
                    messagesPerThread + (totalMessages % producerThreads) : messagesPerThread;
                
                producerExecutor.submit(() -> {
                    kafkaProducerService.sendBulkUserEvents(threadMessages);
                });
            }

            producerExecutor.shutdown();
            producerExecutor.awaitTermination(5, TimeUnit.MINUTES);

            waitForConsumption(totalMessages, () -> kafkaConsumerService.getProcessedUserCount());

            long endTime = System.currentTimeMillis();
            long totalDuration = endTime - startTime;

            PerformanceMetric metric = performanceMonitoringService.endPerformanceTest(
                testName, userEventsTopicName, 4, userEventsPartitions, batchSize);

            return new PerformanceTestResult(
                testName,
                userEventsTopicName,
                totalMessages,
                kafkaConsumerService.getProcessedUserCount(),
                totalDuration,
                metric
            );

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Performance test interrupted", e);
            return null;
        }
    }

    public PerformanceTestResult runPaymentEventsPerformanceTest(int totalMessages, int producerThreads) {
        String testName = "PaymentEvents_" + totalMessages + "_" + producerThreads + "threads";
        
        log.info("Starting payment events performance test: {} messages with {} producer threads", 
                totalMessages, producerThreads);

        kafkaConsumerService.resetCounters();
        performanceMonitoringService.startPerformanceTest(testName, paymentEventsTopicName);

        long startTime = System.currentTimeMillis();

        ExecutorService producerExecutor = Executors.newFixedThreadPool(producerThreads);
        int messagesPerThread = totalMessages / producerThreads;
        
        try {
            for (int i = 0; i < producerThreads; i++) {
                final int threadMessages = (i == producerThreads - 1) ? 
                    messagesPerThread + (totalMessages % producerThreads) : messagesPerThread;
                
                producerExecutor.submit(() -> {
                    kafkaProducerService.sendBulkPaymentEvents(threadMessages);
                });
            }

            producerExecutor.shutdown();
            producerExecutor.awaitTermination(5, TimeUnit.MINUTES);

            waitForConsumption(totalMessages, () -> kafkaConsumerService.getProcessedPaymentCount());

            long endTime = System.currentTimeMillis();
            long totalDuration = endTime - startTime;

            PerformanceMetric metric = performanceMonitoringService.endPerformanceTest(
                testName, paymentEventsTopicName, 4, paymentEventsPartitions, batchSize);

            return new PerformanceTestResult(
                testName,
                paymentEventsTopicName,
                totalMessages,
                kafkaConsumerService.getProcessedPaymentCount(),
                totalDuration,
                metric
            );

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Performance test interrupted", e);
            return null;
        }
    }

    public PerformanceTestResult runMixedEventsPerformanceTest(int totalMessages, int producerThreads) {
        String testName = "MixedEvents_" + totalMessages + "_" + producerThreads + "threads";
        
        log.info("Starting mixed events performance test: {} messages with {} producer threads", 
                totalMessages, producerThreads);

        kafkaConsumerService.resetCounters();
        
        // Start monitoring for all topics
        performanceMonitoringService.startPerformanceTest(testName, "mixed-topics");

        long startTime = System.currentTimeMillis();

        ExecutorService producerExecutor = Executors.newFixedThreadPool(producerThreads);
        int messagesPerType = totalMessages / 3; // Divide equally among 3 event types
        
        try {
            // Send order events
            for (int i = 0; i < producerThreads / 3; i++) {
                producerExecutor.submit(() -> {
                    kafkaProducerService.sendBulkOrderEvents(messagesPerType);
                });
            }

            // Send user events
            for (int i = 0; i < producerThreads / 3; i++) {
                producerExecutor.submit(() -> {
                    kafkaProducerService.sendBulkUserEvents(messagesPerType);
                });
            }

            // Send payment events
            for (int i = 0; i < producerThreads / 3; i++) {
                producerExecutor.submit(() -> {
                    kafkaProducerService.sendBulkPaymentEvents(messagesPerType);
                });
            }

            producerExecutor.shutdown();
            producerExecutor.awaitTermination(5, TimeUnit.MINUTES);

            // Wait for all messages to be consumed
            long expectedTotal = messagesPerType * 3;
            waitForConsumption(expectedTotal, () -> 
                kafkaConsumerService.getProcessedOrderCount() + 
                kafkaConsumerService.getProcessedUserCount() + 
                kafkaConsumerService.getProcessedPaymentCount());

            long endTime = System.currentTimeMillis();
            long totalDuration = endTime - startTime;

            long totalProcessed = kafkaConsumerService.getProcessedOrderCount() + 
                                kafkaConsumerService.getProcessedUserCount() + 
                                kafkaConsumerService.getProcessedPaymentCount();

            return new PerformanceTestResult(
                testName,
                "mixed-topics",
                expectedTotal,
                totalProcessed,
                totalDuration,
                null // No single metric for mixed test
            );

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Performance test interrupted", e);
            return null;
        }
    }

    private void waitForConsumption(long expectedMessages, java.util.function.Supplier<Long> countSupplier) 
            throws InterruptedException {
        long timeout = 300000; // 5 minutes timeout
        long startWait = System.currentTimeMillis();
        
        while (countSupplier.get() < expectedMessages) {
            if (System.currentTimeMillis() - startWait > timeout) {
                log.warn("Timeout waiting for message consumption. Expected: {}, Actual: {}", 
                        expectedMessages, countSupplier.get());
                break;
            }
            Thread.sleep(1000); // Check every second
        }
        
        log.info("Message consumption completed. Expected: {}, Actual: {}", 
                expectedMessages, countSupplier.get());
    }

    public static class PerformanceTestResult {
        public final String testName;
        public final String topic;
        public final long expectedMessages;
        public final long processedMessages;
        public final long durationMs;
        public final double messagesPerSecond;
        public final PerformanceMetric metric;

        public PerformanceTestResult(String testName, String topic, long expectedMessages, 
                                   long processedMessages, long durationMs, PerformanceMetric metric) {
            this.testName = testName;
            this.topic = topic;
            this.expectedMessages = expectedMessages;
            this.processedMessages = processedMessages;
            this.durationMs = durationMs;
            this.messagesPerSecond = durationMs > 0 ? (processedMessages * 1000.0) / durationMs : 0;
            this.metric = metric;
        }
    }
}
